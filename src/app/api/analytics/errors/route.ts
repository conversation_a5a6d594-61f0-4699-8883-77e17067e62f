import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Schema for client-side error reporting
const ClientErrorSchema = z.object({
  message: z.string(),
  stack: z.string().optional(),
  url: z.string(),
  metadata: z.record(z.any()).optional(),
  level: z.enum(['error', 'warning', 'info']).default('error'),
  source: z.string().default('client'),
  userAgent: z.string().optional(),
  environment: z.string().optional(),
});

// POST /api/analytics/errors - Log client-side errors
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = ClientErrorSchema.parse(body);

    // Get user ID from session if available
    let userId: string | undefined;
    try {
      // Try to get session, but don't fail if not available
      const { getServerSession } = await import('next-auth');
      const { authOptions } = await import('@/lib/auth');
      const session = await getServerSession(authOptions);
      userId = session?.user?.id || undefined;
    } catch (error) {
      // Session not available, continue without user ID
    }

    // TODO: Add errorLog model to Prisma schema for proper error tracking
    // For now, just log to console
    const errorData = {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      message: validatedData.message,
      stack: validatedData.stack,
      url: validatedData.url,
      metadata: validatedData.metadata || {},
      level: validatedData.level,
      source: validatedData.source,
      userAgent: validatedData.userAgent,
      userId: userId,
      timestamp: new Date().toISOString(),
    };

    // Log to console for now
    if (validatedData.level === 'error') {
      console.error('Client error logged:', errorData);
    } else if (validatedData.level === 'warning') {
      console.warn('Client warning logged:', errorData);
    } else {
      console.info('Client info logged:', errorData);
    }

    return NextResponse.json({
      success: true,
      errorId: errorData.id,
      message: 'Error logged to console (database logging not yet implemented)',
    });

  } catch (error) {
    console.error('Error logging client error:', error);
    
    // Still return success to avoid client-side error loops
    return NextResponse.json({
      success: true,
      message: 'Error logged with fallback',
    });
  }
}
