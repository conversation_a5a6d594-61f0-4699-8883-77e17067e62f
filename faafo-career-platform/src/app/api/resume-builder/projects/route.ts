import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { z } from 'zod';

const createProjectSchema = z.object({
  resumeId: z.string().uuid('Invalid resume ID'),
  name: z.string().min(1, 'Project name is required').max(100, 'Project name too long'),
  description: z.string().max(2000, 'Description too long').optional(),
  technologies: z.array(z.string().max(50, 'Technology name too long')).default([]),
  url: z.string().url('Invalid project URL').max(255, 'URL too long').optional().or(z.literal('')),
  repositoryUrl: z.string().url('Invalid repository URL').max(255, 'Repository URL too long').optional().or(z.literal('')),
  highlights: z.array(z.string().max(500, 'Highlight too long')).default([]),
  isHighlighted: z.boolean().default(false),
});

const updateProjectSchema = createProjectSchema.partial().extend({
  id: z.string().uuid('Invalid project ID'),
});

interface ErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

interface SuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
}

function errorResponse(
  error: string, 
  status: number = 500, 
  code?: string, 
  details?: any
): NextResponse<ErrorResponse> {
  return NextResponse.json(
    { success: false, error, code, details },
    { status }
  );
}

function successResponse<T>(
  data: T, 
  message?: string, 
  status: number = 200
): NextResponse<SuccessResponse<T>> {
  return NextResponse.json(
    { success: true, data, message },
    { status }
  );
}

// POST - Create new project
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const body = await request.json();
    const validation = createProjectSchema.safeParse(body);
    
    if (!validation.success) {
      return errorResponse(
        'Invalid request data',
        400,
        'VALIDATION_ERROR',
        validation.error.errors
      );
    }

    const data = validation.data;

    // Verify resume ownership
    const resume = await prisma.resume.findFirst({
      where: {
        id: data.resumeId,
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!resume) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    // Create project
    const project = await prisma.resumeProject.create({
      data: {
        resumeId: data.resumeId,
        name: data.name,
        description: data.description || null,
        technologies: data.technologies,
        projectUrl: data.url === '' ? null : data.url,
        repositoryUrl: data.repositoryUrl === '' ? null : data.repositoryUrl,
        highlights: data.highlights,
        isHighlighted: data.isHighlighted,
      },
    });

    return successResponse(project, 'Project created successfully', 201);

  } catch (error) {
    console.error('Error creating project:', error);
    return errorResponse('Failed to create project', 500, 'CREATE_ERROR');
  }
}

// PUT - Update existing project
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const body = await request.json();
    const validation = updateProjectSchema.safeParse(body);
    
    if (!validation.success) {
      return errorResponse(
        'Invalid request data',
        400,
        'VALIDATION_ERROR',
        validation.error.errors
      );
    }

    const { id, ...updateData } = validation.data;

    // Verify ownership through resume
    const project = await prisma.resumeProject.findFirst({
      where: {
        id,
        resume: {
          userId: session.user.id,
          isActive: true,
        },
      },
    });

    if (!project) {
      return errorResponse('Project not found', 404, 'NOT_FOUND');
    }

    // Update project
    const updatedProject = await prisma.resumeProject.update({
      where: { id },
      data: {
        name: updateData.name,
        description: updateData.description || null,
        technologies: updateData.technologies,
        projectUrl: updateData.url === '' ? null : updateData.url,
        repositoryUrl: updateData.repositoryUrl === '' ? null : updateData.repositoryUrl,
        highlights: updateData.highlights,
        isHighlighted: updateData.isHighlighted,
        updatedAt: new Date(),
      },
    });

    return successResponse(updatedProject, 'Project updated successfully');

  } catch (error) {
    console.error('Error updating project:', error);
    return errorResponse('Failed to update project', 500, 'UPDATE_ERROR');
  }
}

// DELETE - Remove project
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return errorResponse('Project ID is required', 400, 'MISSING_ID');
    }

    // Verify ownership through resume
    const project = await prisma.resumeProject.findFirst({
      where: {
        id,
        resume: {
          userId: session.user.id,
          isActive: true,
        },
      },
    });

    if (!project) {
      return errorResponse('Project not found', 404, 'NOT_FOUND');
    }

    // Delete project
    await prisma.resumeProject.delete({
      where: { id },
    });

    return successResponse({ id }, 'Project deleted successfully');

  } catch (error) {
    console.error('Error deleting project:', error);
    return errorResponse('Failed to delete project', 500, 'DELETE_ERROR');
  }
}

// GET - Fetch projects for a resume
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const { searchParams } = new URL(request.url);
    const resumeId = searchParams.get('resumeId');

    if (!resumeId) {
      return errorResponse('Resume ID is required', 400, 'MISSING_RESUME_ID');
    }

    // Verify resume ownership
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!resume) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    // Fetch projects
    const projects = await prisma.resumeProject.findMany({
      where: {
        resumeId,
      },
      orderBy: [
        { isHighlighted: 'desc' },
        { createdAt: 'desc' },
      ],
    });

    return successResponse({ projects });

  } catch (error) {
    console.error('Error fetching projects:', error);
    return errorResponse('Failed to fetch projects', 500, 'FETCH_ERROR');
  }
}
