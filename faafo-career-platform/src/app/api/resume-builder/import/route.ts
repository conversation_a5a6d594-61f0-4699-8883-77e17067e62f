import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { z } from 'zod';

// Schema for complete resume import
const importResumeSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title too long'),
  templateId: z.string().uuid().optional(),
  personalInfo: z.object({
    firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),
    lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
    email: z.string().email('Invalid email').max(255, 'Email too long'),
    phone: z.string().max(20, 'Phone too long').optional(),
    location: z.string().max(100, 'Location too long').optional(),
    website: z.string().max(255, 'Website URL too long').optional().or(z.literal('')),
    linkedin: z.string().max(255, 'LinkedIn URL too long').optional().or(z.literal('')),
  }),
  summary: z.string().max(1000, 'Summary too long').optional(),
  experience: z.array(z.object({
    company: z.string().min(1, 'Company is required').max(100, 'Company name too long'),
    position: z.string().min(1, 'Position is required').max(100, 'Position too long'),
    startDate: z.string().min(1, 'Start date is required'),
    endDate: z.string().optional(),
    current: z.boolean().default(false),
    description: z.string().max(2000, 'Description too long').optional(),
    achievements: z.array(z.string().max(500, 'Achievement too long')).default([]),
  })).default([]),
  education: z.array(z.object({
    institution: z.string().min(1, 'Institution is required').max(100, 'Institution too long'),
    degree: z.string().min(1, 'Degree is required').max(100, 'Degree too long'),
    field: z.string().max(100, 'Field too long').optional(),
    startDate: z.string().min(1, 'Start date is required'),
    endDate: z.string().optional(),
    current: z.boolean().default(false),
    gpa: z.string().max(10, 'GPA too long').optional(),
    achievements: z.array(z.string().max(500, 'Achievement too long')).default([]),
  })).default([]),
  skills: z.array(z.object({
    category: z.string().min(1, 'Category is required').max(50, 'Category too long'),
    skills: z.array(z.object({
      name: z.string().min(1, 'Skill name is required').max(100, 'Skill name too long'),
      proficiency: z.string().optional().default('Intermediate'),
    })),
  })).default([]),
  projects: z.array(z.object({
    name: z.string().min(1, 'Project name is required').max(100, 'Project name too long'),
    description: z.string().max(2000, 'Description too long').optional(),
    technologies: z.array(z.string().max(50, 'Technology name too long')).default([]),
    url: z.string().max(255, 'URL too long').optional().or(z.literal('')),
    highlights: z.array(z.string().max(500, 'Highlight too long')).default([]),
  })).default([]),
});

interface ErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

interface SuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
}

function errorResponse(
  error: string, 
  status: number = 500, 
  code?: string, 
  details?: any
): NextResponse<ErrorResponse> {
  return NextResponse.json(
    { success: false, error, code, details },
    { status }
  );
}

function successResponse<T>(
  data: T, 
  message?: string, 
  status: number = 200
): NextResponse<SuccessResponse<T>> {
  return NextResponse.json(
    { success: true, data, message },
    { status }
  );
}

// POST - Import complete resume with all data
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const body = await request.json();

    const validation = importResumeSchema.safeParse(body);

    if (!validation.success) {
      return errorResponse(
        'Invalid request data',
        400,
        'VALIDATION_ERROR',
        validation.error.errors
      );
    }

    const data = validation.data;

    // Verify template exists if provided
    if (data.templateId) {
      const template = await prisma.resumeTemplate.findFirst({
        where: {
          id: data.templateId,
          isActive: true,
        },
      });

      if (!template) {
        return errorResponse('Invalid template ID', 400, 'INVALID_TEMPLATE');
      }
    }

    // Use transaction to ensure data consistency with enhanced error handling
    const result = await prisma.$transaction(async (tx) => {
      // Check for duplicate resume titles for this user
      const existingResume = await tx.resume.findFirst({
        where: {
          userId: session.user.id,
          title: data.title,
        },
      });

      if (existingResume) {
        // Auto-increment title to avoid conflicts
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:]/g, '');
        data.title = `${data.title} (${timestamp})`;
      }

      // Create the resume with enhanced validation
      const resume = await tx.resume.create({
        data: {
          userId: session.user.id,
          title: data.title,
          templateId: data.templateId,
          firstName: data.personalInfo.firstName.trim(),
          lastName: data.personalInfo.lastName.trim(),
          email: data.personalInfo.email.toLowerCase().trim(),
          phone: data.personalInfo.phone?.trim() || null,
          location: data.personalInfo.location?.trim() || null,
          website: data.personalInfo.website === '' ? null : data.personalInfo.website?.trim(),
          linkedin: data.personalInfo.linkedin === '' ? null : data.personalInfo.linkedin?.trim(),
          summary: data.summary?.trim() || null,
        },
      });

      // PERFORMANCE FIX: Use bulk operations instead of individual creates (N+1 fix)
      const experienceData = data.experience.map(exp => {
        // Validate and sanitize dates
        let startDate: Date;
        let endDate: Date | null = null;

        try {
          startDate = new Date(exp.startDate);
          if (isNaN(startDate.getTime())) {
            throw new Error(`Invalid start date: ${exp.startDate}`);
          }

          if (exp.endDate && !exp.current) {
            endDate = new Date(exp.endDate);
            if (isNaN(endDate.getTime())) {
              throw new Error(`Invalid end date: ${exp.endDate}`);
            }

            // Ensure end date is after start date
            if (endDate < startDate) {
              endDate = startDate;
            }
          }
        } catch (dateError) {
          // Use current date as fallback for invalid dates
          startDate = new Date();
          endDate = exp.current ? null : new Date();
        }

        return {
          resumeId: resume.id,
          company: exp.company.trim(),
          position: exp.position.trim(),
          startDate,
          endDate,
          isCurrent: exp.current,
          description: exp.description?.trim() || null,
          achievements: exp.achievements.filter(a => a.trim().length > 0),
        };
      });

      // Bulk create experiences
      const experiences = experienceData.length > 0
        ? await tx.resumeExperience.createMany({ data: experienceData })
        : { count: 0 };

      // PERFORMANCE FIX: Bulk create education entries
      const educationData = data.education.map(edu => {
        // Validate and sanitize dates
        let startDate: Date;
        let endDate: Date | null = null;

        try {
          startDate = new Date(edu.startDate);
          if (isNaN(startDate.getTime())) {
            throw new Error(`Invalid start date: ${edu.startDate}`);
          }

          if (edu.endDate && !edu.current) {
            endDate = new Date(edu.endDate);
            if (isNaN(endDate.getTime())) {
              throw new Error(`Invalid end date: ${edu.endDate}`);
            }

            // Ensure end date is after start date
            if (endDate < startDate) {
              endDate = startDate;
            }
          }
        } catch (dateError) {
          // Use current date as fallback for invalid dates
          startDate = new Date();
          endDate = edu.current ? null : new Date();
        }

        return {
          resumeId: resume.id,
          institution: edu.institution.trim(),
          degree: edu.degree.trim(),
          fieldOfStudy: edu.field?.trim() || null,
          startDate,
          endDate,
          gpa: edu.gpa?.trim() || null,
          honors: edu.achievements.filter(a => a.trim().length > 0),
        };
      });

      // Bulk create education
      const education = educationData.length > 0
        ? await tx.resumeEducation.createMany({ data: educationData })
        : { count: 0 };

      // PERFORMANCE FIX: Bulk create skills entries
      const skillsData = data.skills.flatMap(skillCategory =>
        skillCategory.skills
          .filter(skill => skill.name.trim().length > 0) // Filter out empty skills
          .map(skill => {
            // Convert proficiency to number (1-5 scale)
            let proficiencyLevel = null;
            if (skill.proficiency) {
              const proficiencyMap: { [key: string]: number } = {
                'beginner': 1,
                'intermediate': 3,
                'advanced': 4,
                'expert': 5
              };
              proficiencyLevel = proficiencyMap[skill.proficiency.toLowerCase()] || 3;
            }

            return {
              resumeId: resume.id,
              skillName: skill.name.trim(),
              category: skillCategory.category.trim(),
              proficiency: proficiencyLevel,
            };
          })
      );

      // Bulk create skills
      const skills = skillsData.length > 0
        ? await tx.resumeSkill.createMany({ data: skillsData })
        : { count: 0 };

      // PERFORMANCE FIX: Bulk create project entries
      const projectsData = data.projects
        .filter(project => project.name.trim().length > 0) // Filter out empty projects
        .map(project => ({
          resumeId: resume.id,
          name: project.name.trim(),
          description: project.description?.trim() || null,
          technologies: project.technologies.filter(tech => tech.trim().length > 0),
          projectUrl: project.url === '' ? null : project.url?.trim(),
          isHighlighted: false,
        }));

      // Bulk create projects
      const projects = projectsData.length > 0
        ? await tx.resumeProject.createMany({ data: projectsData })
        : { count: 0 };

      return {
        resume,
        experiences,
        education,
        skills,
        projects,
        counts: {
          experiences: experiences.count || 0,
          education: education.count || 0,
          skills: skills.count || 0,
          projects: projects.count || 0,
        },
      };
    });

    return successResponse(
      result,
      `Resume imported successfully with ${result.counts.experiences} experiences, ${result.counts.education} education entries, ${result.counts.skills} skills, and ${result.counts.projects} projects`,
      201
    );

  } catch (error) {
    console.error('Error importing resume:', error);

    // Enhanced error handling for specific cases
    if (error instanceof Error) {
      // Handle specific Prisma errors
      if (error.message.includes('Unique constraint')) {
        return errorResponse('A resume with this title already exists', 409, 'DUPLICATE_TITLE');
      }

      // Handle validation errors
      if (error.message.includes('Invalid') || error.message.includes('required')) {
        return errorResponse(`Validation error: ${error.message}`, 400, 'VALIDATION_ERROR');
      }

      // Handle database connection errors
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        return errorResponse('Database connection error. Please try again.', 503, 'DATABASE_ERROR');
      }

      // Handle foreign key constraint errors
      if (error.message.includes('Foreign key constraint')) {
        return errorResponse('Invalid template or user reference', 400, 'REFERENCE_ERROR');
      }
    }

    return errorResponse('Failed to import resume. Please check your data and try again.', 500, 'IMPORT_ERROR');
  }
}
