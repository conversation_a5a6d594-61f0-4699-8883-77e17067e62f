/**
 * SECURITY UTILITIES
 * Comprehensive security utilities to prevent injection attacks, XSS, and other vulnerabilities
 */

import { z } from 'zod';
import * as crypto from 'crypto';

export class SecurityValidator {
  /**
   * Sanitize string input to prevent XSS and injection attacks
   */
  static sanitizeString(input: string, maxLength: number = 1000): string {
    if (typeof input !== 'string') {
      throw new Error('Input must be a string');
    }

    return input
      .replace(/[<>]/g, '') // Remove HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/data:/gi, '') // Remove data: protocol
      .replace(/vbscript:/gi, '') // Remove vbscript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .replace(/eval\s*\(/gi, '') // Remove eval calls
      .replace(/Function\s*\(/gi, '') // Remove Function constructor
      .replace(/setTimeout\s*\(/gi, '') // Remove setTimeout
      .replace(/setInterval\s*\(/gi, '') // Remove setInterval
      .replace(/document\./gi, '') // Remove document access
      .replace(/window\./gi, '') // Remove window access
      .substring(0, maxLength)
      .trim();
  }

  /**
   * Validate and sanitize email addresses
   */
  static sanitizeEmail(email: string): string {
    const emailSchema = z.string().email().max(255);
    const validated = emailSchema.parse(email.toLowerCase().trim());
    
    // Additional security checks
    if (validated.includes('<') || validated.includes('>') || validated.includes('javascript:')) {
      throw new Error('Invalid email format');
    }
    
    return validated;
  }

  /**
   * Validate and sanitize URLs
   */
  static sanitizeUrl(url: string): string {
    if (!url || url === '') return '';
    
    const urlSchema = z.string().url().max(500);
    const validated = urlSchema.parse(url.trim());
    
    // Check for dangerous protocols
    const dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'file:', 'ftp:'];
    const lowerUrl = validated.toLowerCase();
    
    for (const protocol of dangerousProtocols) {
      if (lowerUrl.startsWith(protocol)) {
        throw new Error('Invalid URL protocol');
      }
    }
    
    return validated;
  }

  /**
   * Validate file uploads
   */
  static validateFileUpload(file: File): void {
    const maxFileSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'application/rtf'
    ];

    // Size validation
    if (file.size > maxFileSize) {
      throw new Error('File too large. Maximum size is 10MB.');
    }

    // Type validation
    if (!allowedTypes.includes(file.type)) {
      throw new Error('Invalid file type. Only PDF, DOCX, TXT, and RTF files are allowed.');
    }

    // Name validation
    const fileName = file.name.toLowerCase();
    if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
      throw new Error('Invalid file name.');
    }

    // Extension validation
    const allowedExtensions = ['.pdf', '.docx', '.txt', '.rtf'];
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
    
    if (!hasValidExtension) {
      throw new Error('Invalid file extension.');
    }
  }

  /**
   * Check for prototype pollution attempts
   */
  static hasPrototypePollution(obj: any): boolean {
    const dangerousKeys = ['__proto__', 'constructor', 'prototype'];
    
    function checkObject(current: any, depth = 0): boolean {
      if (depth > 10) return false; // Prevent deep recursion
      
      if (typeof current !== 'object' || current === null) return false;
      
      for (const key in current) {
        if (dangerousKeys.includes(key)) return true;
        if (typeof current[key] === 'object' && checkObject(current[key], depth + 1)) {
          return true;
        }
      }
      return false;
    }
    
    return checkObject(obj);
  }

  /**
   * Generate cryptographically secure hash
   */
  static generateSecureHash(content: string): string {
    try {
      return crypto.createHash('sha256').update(content, 'utf8').digest('hex').substring(0, 16);
    } catch (error) {
      console.error('Error generating secure hash:', error);
      return `fallback_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    }
  }

  /**
   * Validate JSON structure to prevent malformed data
   */
  static isValidJsonStructure(jsonString: string): boolean {
    try {
      if (!jsonString.trim().startsWith('{') || !jsonString.trim().endsWith('}')) {
        return false;
      }
      
      // Check for balanced braces
      let braceCount = 0;
      for (const char of jsonString) {
        if (char === '{') braceCount++;
        if (char === '}') braceCount--;
        if (braceCount < 0) return false;
      }
      
      return braceCount === 0;
    } catch {
      return false;
    }
  }

  /**
   * Sanitize object recursively to prevent injection
   */
  static sanitizeObject(obj: any, maxDepth: number = 5): any {
    if (maxDepth <= 0) return null;
    
    if (typeof obj === 'string') {
      return this.sanitizeString(obj);
    }
    
    if (Array.isArray(obj)) {
      return obj.slice(0, 100).map(item => this.sanitizeObject(item, maxDepth - 1));
    }
    
    if (typeof obj === 'object' && obj !== null) {
      // Check for prototype pollution
      if (this.hasPrototypePollution(obj)) {
        throw new Error('Potential prototype pollution detected');
      }
      
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        // Sanitize key names
        const sanitizedKey = key.replace(/[^a-zA-Z0-9_]/g, '').substring(0, 100);
        if (sanitizedKey && !['__proto__', 'constructor', 'prototype'].includes(sanitizedKey)) {
          sanitized[sanitizedKey] = this.sanitizeObject(value, maxDepth - 1);
        }
      }
      return sanitized;
    }
    
    return obj;
  }

  /**
   * Rate limiting check (to be used with Redis or in-memory store)
   */
  static checkRateLimit(identifier: string, maxRequests: number = 10, windowMs: number = 60000): boolean {
    // This would typically use Redis or a proper rate limiting service
    // For now, return true (allow) - implement with actual rate limiting service
    return true;
  }

  /**
   * Validate request size to prevent DoS attacks
   */
  static validateRequestSize(request: Request, maxSize: number = 1024 * 1024): void {
    const contentLength = request.headers.get('content-length');
    if (contentLength && parseInt(contentLength) > maxSize) {
      throw new Error('Request too large');
    }
  }

  /**
   * Comprehensive input validation for resume data
   */
  static validateResumeInput(data: any): void {
    // Check for dangerous patterns
    const dangerousPatterns = [
      /<script/i,
      /javascript:/i,
      /data:/i,
      /vbscript:/i,
      /on\w+\s*=/i,
      /eval\s*\(/i,
      /Function\s*\(/i
    ];

    const jsonString = JSON.stringify(data);
    for (const pattern of dangerousPatterns) {
      if (pattern.test(jsonString)) {
        throw new Error('Potentially dangerous content detected');
      }
    }

    // Check for prototype pollution
    if (this.hasPrototypePollution(data)) {
      throw new Error('Potential prototype pollution detected');
    }

    // Validate data size
    if (jsonString.length > 100000) { // 100KB limit
      throw new Error('Data too large');
    }
  }
}

/**
 * Middleware wrapper for security validation
 */
export function withSecurityValidation<T extends (...args: any[]) => any>(
  handler: T,
  options: {
    validateInput?: boolean;
    sanitizeOutput?: boolean;
    maxRequestSize?: number;
  } = {}
): T {
  return (async (...args: any[]) => {
    try {
      const [request] = args;
      
      // Validate request size
      if (options.maxRequestSize && request?.headers) {
        SecurityValidator.validateRequestSize(request, options.maxRequestSize);
      }
      
      // Validate input if enabled
      if (options.validateInput && request?.json) {
        const body = await request.json();
        SecurityValidator.validateResumeInput(body);
      }
      
      const result = await handler(...args);
      
      // Sanitize output if enabled
      if (options.sanitizeOutput && result?.json) {
        const sanitized = SecurityValidator.sanitizeObject(result.json);
        return { ...result, json: sanitized };
      }
      
      return result;
    } catch (error) {
      console.error('Security validation error:', error);
      throw error;
    }
  }) as T;
}

export default SecurityValidator;
