/**
 * COMPREHENSIVE SECURITY MIDDLEWARE
 * Addresses missing security measures including rate limiting, input validation, and security headers
 */

import { NextRequest, NextResponse } from 'next/server';
import SecurityValidator from './securityUtils';

interface SecurityConfig {
  rateLimit?: {
    maxRequests: number;
    windowMs: number;
    message?: string;
  };
  fileUpload?: {
    maxSize: number;
    allowedTypes: string[];
  };
  requestValidation?: {
    maxBodySize: number;
    validateInput: boolean;
  };
  securityHeaders?: boolean;
}

// In-memory rate limiting store (use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

/**
 * Rate limiting implementation
 */
export function rateLimit(
  identifier: string,
  maxRequests: number = 10,
  windowMs: number = 60000
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const key = identifier;
  
  // Clean up expired entries
  const expiredKeys: string[] = [];
  rateLimitStore.forEach((v, k) => {
    if (v.resetTime < now) {
      expiredKeys.push(k);
    }
  });
  expiredKeys.forEach(key => rateLimitStore.delete(key));
  
  const current = rateLimitStore.get(key);
  
  if (!current || current.resetTime < now) {
    // First request or window expired
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + windowMs
    });
    return {
      allowed: true,
      remaining: maxRequests - 1,
      resetTime: now + windowMs
    };
  }
  
  if (current.count >= maxRequests) {
    // Rate limit exceeded
    return {
      allowed: false,
      remaining: 0,
      resetTime: current.resetTime
    };
  }
  
  // Increment count
  current.count++;
  rateLimitStore.set(key, current);
  
  return {
    allowed: true,
    remaining: maxRequests - current.count,
    resetTime: current.resetTime
  };
}

/**
 * Security headers middleware
 */
export function addSecurityHeaders(response: NextResponse): NextResponse {
  // Prevent XSS attacks
  response.headers.set('X-XSS-Protection', '1; mode=block');
  
  // Prevent clickjacking
  response.headers.set('X-Frame-Options', 'DENY');
  
  // Prevent MIME type sniffing
  response.headers.set('X-Content-Type-Options', 'nosniff');
  
  // Referrer policy
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Content Security Policy
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';"
  );
  
  // Strict Transport Security (HTTPS only)
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  }
  
  return response;
}

/**
 * Comprehensive security middleware
 */
export function withSecurity(
  handler: (request: NextRequest) => Promise<NextResponse>,
  config: SecurityConfig = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      // 1. Rate limiting
      if (config.rateLimit) {
        const identifier = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
        const rateLimitResult = rateLimit(
          identifier,
          config.rateLimit.maxRequests,
          config.rateLimit.windowMs
        );
        
        if (!rateLimitResult.allowed) {
          const response = NextResponse.json(
            {
              success: false,
              error: config.rateLimit.message || 'Too many requests. Please try again later.',
              code: 'RATE_LIMIT_EXCEEDED'
            },
            { status: 429 }
          );
          
          response.headers.set('X-RateLimit-Limit', config.rateLimit.maxRequests.toString());
          response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
          response.headers.set('X-RateLimit-Reset', rateLimitResult.resetTime.toString());
          
          return addSecurityHeaders(response);
        }
        
        // Add rate limit headers to successful responses
        const rateLimitHeaders = {
          'X-RateLimit-Limit': config.rateLimit.maxRequests.toString(),
          'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
          'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
        };
      }
      
      // 2. Request size validation
      if (config.requestValidation?.maxBodySize) {
        const contentLength = request.headers.get('content-length');
        if (contentLength && parseInt(contentLength) > config.requestValidation.maxBodySize) {
          return addSecurityHeaders(
            NextResponse.json(
              {
                success: false,
                error: 'Request too large',
                code: 'REQUEST_TOO_LARGE'
              },
              { status: 413 }
            )
          );
        }
      }
      
      // 3. Input validation for JSON requests
      if (config.requestValidation?.validateInput && request.method !== 'GET') {
        try {
          const contentType = request.headers.get('content-type');
          if (contentType?.includes('application/json')) {
            const body = await request.clone().json();
            SecurityValidator.validateResumeInput(body);
          }
        } catch (error) {
          return addSecurityHeaders(
            NextResponse.json(
              {
                success: false,
                error: error instanceof Error ? error.message : 'Invalid input data',
                code: 'VALIDATION_ERROR'
              },
              { status: 400 }
            )
          );
        }
      }
      
      // 4. File upload validation
      if (config.fileUpload && request.method === 'POST') {
        const contentType = request.headers.get('content-type');
        if (contentType?.includes('multipart/form-data')) {
          try {
            const formData = await request.clone().formData();
            const file = formData.get('file') as File;
            
            if (file) {
              // Override file validation with config
              const maxSize = config.fileUpload.maxSize;
              const allowedTypes = config.fileUpload.allowedTypes;
              
              if (file.size > maxSize) {
                throw new Error(`File too large. Maximum size is ${Math.round(maxSize / 1024 / 1024)}MB.`);
              }
              
              if (!allowedTypes.includes(file.type)) {
                throw new Error(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`);
              }
              
              SecurityValidator.validateFileUpload(file);
            }
          } catch (error) {
            return addSecurityHeaders(
              NextResponse.json(
                {
                  success: false,
                  error: error instanceof Error ? error.message : 'File validation failed',
                  code: 'FILE_VALIDATION_ERROR'
                },
                { status: 400 }
              )
            );
          }
        }
      }
      
      // 5. Execute the handler
      const response = await handler(request);
      
      // 6. Add security headers
      if (config.securityHeaders !== false) {
        return addSecurityHeaders(response);
      }
      
      return response;
      
    } catch (error) {
      console.error('Security middleware error:', error);
      
      return addSecurityHeaders(
        NextResponse.json(
          {
            success: false,
            error: 'Internal security error',
            code: 'SECURITY_ERROR'
          },
          { status: 500 }
        )
      );
    }
  };
}

/**
 * Predefined security configurations
 */
export const SecurityConfigs = {
  // For resume import endpoints
  resumeImport: {
    rateLimit: {
      maxRequests: 10,
      windowMs: 60000, // 1 minute
      message: 'Too many resume import requests. Please try again later.'
    },
    requestValidation: {
      maxBodySize: 1024 * 1024, // 1MB
      validateInput: true
    },
    securityHeaders: true
  },
  
  // For file upload endpoints
  fileUpload: {
    rateLimit: {
      maxRequests: 5,
      windowMs: 60000, // 1 minute
      message: 'Too many file upload requests. Please try again later.'
    },
    fileUpload: {
      maxSize: 10 * 1024 * 1024, // 10MB
      allowedTypes: [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'application/rtf'
      ]
    },
    requestValidation: {
      maxBodySize: 10 * 1024 * 1024, // 10MB
      validateInput: false // File uploads don't need JSON validation
    },
    securityHeaders: true
  },
  
  // For AI processing endpoints
  aiProcessing: {
    rateLimit: {
      maxRequests: 15,
      windowMs: 15 * 60000, // 15 minutes
      message: 'Too many AI processing requests. Please try again later.'
    },
    requestValidation: {
      maxBodySize: 100 * 1024, // 100KB
      validateInput: true
    },
    securityHeaders: true
  },
  
  // For general API endpoints
  general: {
    rateLimit: {
      maxRequests: 100,
      windowMs: 60000, // 1 minute
      message: 'Too many requests. Please try again later.'
    },
    requestValidation: {
      maxBodySize: 1024 * 1024, // 1MB
      validateInput: true
    },
    securityHeaders: true
  }
};

/**
 * Convenience functions for common use cases
 */
export const withResumeImportSecurity = (handler: (request: NextRequest) => Promise<NextResponse>) =>
  withSecurity(handler, SecurityConfigs.resumeImport);

export const withFileUploadSecurity = (handler: (request: NextRequest) => Promise<NextResponse>) =>
  withSecurity(handler, SecurityConfigs.fileUpload);

export const withAIProcessingSecurity = (handler: (request: NextRequest) => Promise<NextResponse>) =>
  withSecurity(handler, SecurityConfigs.aiProcessing);

export const withGeneralSecurity = (handler: (request: NextRequest) => Promise<NextResponse>) =>
  withSecurity(handler, SecurityConfigs.general);

export default withSecurity;
